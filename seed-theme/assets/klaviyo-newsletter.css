/* Klaviyo Newsletter Widget Enhanced Styles */

/* Main container styling */
.klaviyo-newsletter-widget {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Form container */
.klaviyo-newsletter-widget .klaviyo-form,
.klaviyo-newsletter-widget [class*="klaviyo"] {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  font-family: var(--main_ff) !important;
}

/* Content wrapper */
.klaviyo-newsletter-widget .klaviyo-form-content,
.klaviyo-newsletter-widget [class*="content"],
.klaviyo-newsletter-widget [class*="wrapper"] {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Typography styles */
.klaviyo-newsletter-widget h1,
.klaviyo-newsletter-widget h2,
.klaviyo-newsletter-widget h3,
.klaviyo-newsletter-widget h4,
.klaviyo-newsletter-widget h5,
.klaviyo-newsletter-widget h6 {
  color: var(--custom_footer_fg) !important;
  font-family: var(--main_ff) !important;
  font-size: var(--main_h_small) !important;
  font-weight: var(--main_fw_strong) !important;
  margin: 0 0 8px 0 !important;
  padding: 0 !important;
  line-height: 1.2 !important;
  text-align: left !important;
}

.klaviyo-newsletter-widget p,
.klaviyo-newsletter-widget div:not([class*="field"]):not([class*="button"]) {
  color: var(--custom_footer_fg) !important;
  font-family: var(--main_ff) !important;
  font-size: var(--main_fz) !important;
  line-height: 1.6 !important;
  margin: 0 0 12px 0 !important;
  padding: 0 !important;
  text-align: left !important;
}

/* Field group styling */
.klaviyo-newsletter-widget .klaviyo-field-group,
.klaviyo-newsletter-widget [class*="field-group"],
.klaviyo-newsletter-widget [class*="input-group"] {
  display: flex !important;
  gap: 8px !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  align-items: flex-end !important;
}

/* Input field styling */
.klaviyo-newsletter-widget input[type="email"],
.klaviyo-newsletter-widget input[type="text"],
.klaviyo-newsletter-widget input[type="tel"] {
  flex: 1 !important;
  width: auto !important;
  max-width: none !important;
  min-width: 0 !important;
  padding: 12px 16px !important;
  border: 1px solid var(--custom_input_bd) !important;
  border-radius: 4px !important;
  background: var(--custom_input_bg) !important;
  color: var(--primary_text) !important;
  font-family: var(--main_ff) !important;
  font-size: var(--main_fz) !important;
  line-height: 1.4 !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  transition: border-color 0.3s ease, box-shadow 0.3s ease !important;
}

.klaviyo-newsletter-widget input[type="email"]:focus,
.klaviyo-newsletter-widget input[type="text"]:focus,
.klaviyo-newsletter-widget input[type="tel"]:focus {
  outline: none !important;
  border-color: var(--secondary_bg) !important;
  box-shadow: 0 0 0 2px rgba(var(--secondary_bg_rgb), 0.1) !important;
}

.klaviyo-newsletter-widget input::placeholder {
  color: var(--gray_text) !important;
  opacity: 0.7 !important;
}

/* Button styling */
.klaviyo-newsletter-widget button[type="submit"],
.klaviyo-newsletter-widget input[type="submit"],
.klaviyo-newsletter-widget [class*="submit"],
.klaviyo-newsletter-widget [class*="button"] {
  padding: 12px 24px !important;
  background: var(--custom_footer_link_bg) !important;
  color: var(--custom_footer_link_text) !important;
  border: none !important;
  border-radius: 4px !important;
  font-family: var(--main_ff) !important;
  font-size: var(--main_fz) !important;
  font-weight: var(--main_fw_strong) !important;
  cursor: pointer !important;
  transition: background-color 0.3s ease !important;
  white-space: nowrap !important;
  min-width: 120px !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1.4 !important;
}

.klaviyo-newsletter-widget button[type="submit"]:hover,
.klaviyo-newsletter-widget input[type="submit"]:hover,
.klaviyo-newsletter-widget [class*="submit"]:hover,
.klaviyo-newsletter-widget [class*="button"]:hover {
  background: var(--custom_footer_link_dark) !important;
  transform: translateY(-1px) !important;
}

/* Success/Error messages */
.klaviyo-newsletter-widget .success-message,
.klaviyo-newsletter-widget .error-message,
.klaviyo-newsletter-widget [class*="message"] {
  padding: 8px 12px !important;
  border-radius: 4px !important;
  font-family: var(--main_ff) !important;
  font-size: var(--main_fz) !important;
  margin: 8px 0 !important;
}

.klaviyo-newsletter-widget .success-message {
  background: rgba(34, 197, 94, 0.1) !important;
  color: rgb(34, 197, 94) !important;
  border: 1px solid rgba(34, 197, 94, 0.3) !important;
}

.klaviyo-newsletter-widget .error-message {
  background: rgba(239, 68, 68, 0.1) !important;
  color: rgb(239, 68, 68) !important;
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
}

/* Mobile responsive styles */
@media only screen and (max-width: 47.5em) {
  .klaviyo-newsletter-widget .klaviyo-field-group,
  .klaviyo-newsletter-widget [class*="field-group"],
  .klaviyo-newsletter-widget [class*="input-group"] {
    flex-direction: column !important;
    gap: 12px !important;
    align-items: stretch !important;
  }

  .klaviyo-newsletter-widget button[type="submit"],
  .klaviyo-newsletter-widget input[type="submit"],
  .klaviyo-newsletter-widget [class*="submit"],
  .klaviyo-newsletter-widget [class*="button"] {
    width: 100% !important;
    min-width: auto !important;
  }

  .klaviyo-newsletter-widget input[type="email"],
  .klaviyo-newsletter-widget input[type="text"],
  .klaviyo-newsletter-widget input[type="tel"] {
    width: 100% !important;
  }
}

/* Override any conflicting styles */
.klaviyo-newsletter-widget * {
  box-sizing: border-box !important;
}

/* Hide any unwanted elements */
.klaviyo-newsletter-widget .klaviyo-close-form,
.klaviyo-newsletter-widget [class*="close"] {
  display: none !important;
}

/* Ensure proper spacing in footer context */
.shopify-section-footer .klaviyo-newsletter-widget {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Additional overrides for common Klaviyo classes */
.klaviyo-newsletter-widget .kl-private-reset-css-Xuajs1 {
  all: unset !important;
}

.klaviyo-newsletter-widget [class*="kl-"] {
  font-family: var(--main_ff) !important;
  color: var(--custom_footer_fg) !important;
}

/* Enhanced styling to match reference design */
.klaviyo-newsletter-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Title styling to match "Step with us!" */
.klaviyo-newsletter-widget h1,
.klaviyo-newsletter-widget h2,
.klaviyo-newsletter-widget h3,
.klaviyo-newsletter-widget h4 {
  font-size: 18px !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  color: var(--custom_footer_fg) !important;
}

/* Subtitle styling */
.klaviyo-newsletter-widget p {
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin-bottom: 16px !important;
  color: var(--custom_footer_fg) !important;
  opacity: 0.9 !important;
}

/* Input and button container */
.klaviyo-newsletter-widget .klaviyo-field-group,
.klaviyo-newsletter-widget [class*="field-group"],
.klaviyo-newsletter-widget [class*="input-group"] {
  display: flex !important;
  gap: 0 !important;
  border-radius: 6px !important;
  overflow: hidden !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Email input styling to match reference */
.klaviyo-newsletter-widget input[type="email"] {
  flex: 1 !important;
  padding: 12px 16px !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: var(--custom_footer_fg) !important;
  font-size: 14px !important;
  font-family: var(--main_ff) !important;
  outline: none !important;
}

.klaviyo-newsletter-widget input[type="email"]::placeholder {
  color: rgba(var(--custom_footer_fg_rgb, 255, 255, 255), 0.7) !important;
}

.klaviyo-newsletter-widget input[type="email"]:focus {
  background: rgba(255, 255, 255, 0.05) !important;
}

/* Submit button styling to match reference */
.klaviyo-newsletter-widget button[type="submit"],
.klaviyo-newsletter-widget input[type="submit"] {
  padding: 12px 20px !important;
  background: #4a5568 !important;
  color: white !important;
  border: none !important;
  border-radius: 0 !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  font-family: var(--main_ff) !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  min-width: 100px !important;
}

.klaviyo-newsletter-widget button[type="submit"]:hover,
.klaviyo-newsletter-widget input[type="submit"]:hover {
  background: #2d3748 !important;
}

/* Loading state */
.klaviyo-newsletter-widget.klaviyo-loading button[type="submit"],
.klaviyo-newsletter-widget.klaviyo-loading input[type="submit"] {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}

/* Success state */
.klaviyo-newsletter-widget.klaviyo-success {
  border-color: #48bb78 !important;
}

/* Error state */
.klaviyo-newsletter-widget.klaviyo-error {
  border-color: #f56565 !important;
}

/* Message styling */
.klaviyo-newsletter-widget .klaviyo-message {
  padding: 8px 12px !important;
  border-radius: 4px !important;
  font-size: 13px !important;
  margin-top: 8px !important;
  font-family: var(--main_ff) !important;
}

.klaviyo-newsletter-widget .klaviyo-message-success {
  background: rgba(72, 187, 120, 0.1) !important;
  color: #48bb78 !important;
  border: 1px solid rgba(72, 187, 120, 0.3) !important;
}

.klaviyo-newsletter-widget .klaviyo-message-error {
  background: rgba(245, 101, 101, 0.1) !important;
  color: #f56565 !important;
  border: 1px solid rgba(245, 101, 101, 0.3) !important;
}

/* Validation message */
.klaviyo-newsletter-widget .validation-message {
  color: #f56565 !important;
  font-size: 12px !important;
  margin-top: 4px !important;
  font-family: var(--main_ff) !important;
}

/* Mobile responsive adjustments */
@media only screen and (max-width: 47.5em) {
  .klaviyo-newsletter-widget .klaviyo-field-group,
  .klaviyo-newsletter-widget [class*="field-group"],
  .klaviyo-newsletter-widget [class*="input-group"] {
    flex-direction: column !important;
    border-radius: 6px !important;
  }

  .klaviyo-newsletter-widget input[type="email"] {
    border-radius: 6px 6px 0 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .klaviyo-newsletter-widget button[type="submit"],
  .klaviyo-newsletter-widget input[type="submit"] {
    border-radius: 0 0 6px 6px !important;
    width: 100% !important;
  }
}
