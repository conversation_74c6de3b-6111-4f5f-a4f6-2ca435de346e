/**
 * Klaviyo Newsletter Widget Enhancement Script
 * Improves the styling and functionality of Klaviyo forms in the footer
 */

(function() {
  'use strict';

  // Configuration
  const CONFIG = {
    selectors: {
      widget: '.klaviyo-newsletter-widget',
      form: '.klaviyo-form',
      iframe: 'iframe[src*="klaviyo"]',
      emailInput: 'input[type="email"]',
      submitButton: 'button[type="submit"], input[type="submit"]'
    },
    classes: {
      enhanced: 'klaviyo-enhanced',
      loading: 'klaviyo-loading',
      success: 'klaviyo-success',
      error: 'klaviyo-error'
    },
    debounceDelay: 100
  };

  // Utility functions
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  const waitForElement = (selector, timeout = 5000) => {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  };

  // Main enhancement functions
  const enhanceKlaviyoWidget = (widget) => {
    if (widget.classList.contains(CONFIG.classes.enhanced)) {
      return;
    }

    widget.classList.add(CONFIG.classes.enhanced);

    // Find and enhance form elements
    const form = widget.querySelector(CONFIG.selectors.form) || widget.querySelector('form');
    if (form) {
      enhanceForm(form);
    }

    // Handle iframe-based forms
    const iframe = widget.querySelector(CONFIG.selectors.iframe);
    if (iframe) {
      enhanceIframeForm(iframe);
    }

    // Add custom event listeners
    addCustomEventListeners(widget);
  };

  const enhanceForm = (form) => {
    // Enhance email input
    const emailInput = form.querySelector(CONFIG.selectors.emailInput);
    if (emailInput) {
      enhanceEmailInput(emailInput);
    }

    // Enhance submit button
    const submitButton = form.querySelector(CONFIG.selectors.submitButton);
    if (submitButton) {
      enhanceSubmitButton(submitButton);
    }

    // Add form validation
    addFormValidation(form);
  };

  const enhanceEmailInput = (input) => {
    // Improve placeholder text if needed
    if (!input.placeholder || input.placeholder.toLowerCase().includes('email')) {
      input.placeholder = 'Enter your email address';
    }

    // Add input validation styling
    input.addEventListener('input', debounce((e) => {
      const isValid = e.target.validity.valid && e.target.value.length > 0;
      e.target.classList.toggle('valid', isValid);
      e.target.classList.toggle('invalid', !isValid && e.target.value.length > 0);
    }, CONFIG.debounceDelay));

    // Add focus/blur effects
    input.addEventListener('focus', () => {
      input.parentElement?.classList.add('focused');
    });

    input.addEventListener('blur', () => {
      input.parentElement?.classList.remove('focused');
    });
  };

  const enhanceSubmitButton = (button) => {
    // Ensure button has proper text
    if (!button.textContent.trim() || button.textContent.toLowerCase().includes('submit')) {
      button.textContent = 'Subscribe';
    }

    // Add loading state handling
    const originalText = button.textContent;
    
    button.addEventListener('click', () => {
      button.classList.add(CONFIG.classes.loading);
      button.textContent = 'Subscribing...';
      button.disabled = true;

      // Reset after timeout (fallback)
      setTimeout(() => {
        button.classList.remove(CONFIG.classes.loading);
        button.textContent = originalText;
        button.disabled = false;
      }, 5000);
    });
  };

  const enhanceIframeForm = (iframe) => {
    // Add responsive handling for iframe
    iframe.style.width = '100%';
    iframe.style.border = 'none';
    
    // Try to communicate with iframe content
    iframe.addEventListener('load', () => {
      try {
        // Attempt to style iframe content (if same-origin)
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        if (iframeDoc) {
          const style = iframeDoc.createElement('style');
          style.textContent = `
            body { 
              font-family: var(--main_ff, inherit) !important; 
              margin: 0 !important; 
              padding: 0 !important; 
            }
            input[type="email"] { 
              width: 100% !important; 
              box-sizing: border-box !important; 
            }
          `;
          iframeDoc.head.appendChild(style);
        }
      } catch (e) {
        // Cross-origin iframe, can't access content
        console.log('Klaviyo iframe is cross-origin, cannot enhance directly');
      }
    });
  };

  const addFormValidation = (form) => {
    form.addEventListener('submit', (e) => {
      const emailInput = form.querySelector(CONFIG.selectors.emailInput);
      
      if (emailInput && !emailInput.validity.valid) {
        e.preventDefault();
        emailInput.focus();
        showValidationMessage(emailInput, 'Please enter a valid email address');
        return false;
      }
    });
  };

  const addCustomEventListeners = (widget) => {
    // Listen for Klaviyo success/error events
    window.addEventListener('message', (event) => {
      if (event.data && typeof event.data === 'object') {
        if (event.data.type === 'klaviyo_form_success') {
          handleFormSuccess(widget);
        } else if (event.data.type === 'klaviyo_form_error') {
          handleFormError(widget, event.data.message);
        }
      }
    });
  };

  const handleFormSuccess = (widget) => {
    widget.classList.add(CONFIG.classes.success);
    widget.classList.remove(CONFIG.classes.error, CONFIG.classes.loading);
    
    // Show success message
    showMessage(widget, 'Thank you for subscribing!', 'success');
    
    // Reset form after delay
    setTimeout(() => {
      widget.classList.remove(CONFIG.classes.success);
      const form = widget.querySelector('form');
      if (form) form.reset();
    }, 3000);
  };

  const handleFormError = (widget, message) => {
    widget.classList.add(CONFIG.classes.error);
    widget.classList.remove(CONFIG.classes.success, CONFIG.classes.loading);
    
    showMessage(widget, message || 'Something went wrong. Please try again.', 'error');
    
    // Reset error state after delay
    setTimeout(() => {
      widget.classList.remove(CONFIG.classes.error);
    }, 5000);
  };

  const showMessage = (widget, text, type) => {
    // Remove existing messages
    const existingMessages = widget.querySelectorAll('.klaviyo-message');
    existingMessages.forEach(msg => msg.remove());
    
    // Create new message
    const message = document.createElement('div');
    message.className = `klaviyo-message klaviyo-message-${type}`;
    message.textContent = text;
    
    // Insert message
    const form = widget.querySelector('form') || widget;
    form.appendChild(message);
    
    // Auto-remove after delay
    setTimeout(() => {
      message.remove();
    }, type === 'success' ? 3000 : 5000);
  };

  const showValidationMessage = (input, message) => {
    // Remove existing validation messages
    const existingMsg = input.parentElement?.querySelector('.validation-message');
    if (existingMsg) existingMsg.remove();
    
    // Create validation message
    const validationMsg = document.createElement('div');
    validationMsg.className = 'validation-message';
    validationMsg.textContent = message;
    
    // Insert after input
    input.parentElement?.appendChild(validationMsg);
    
    // Remove after delay
    setTimeout(() => {
      validationMsg.remove();
    }, 3000);
  };

  // Initialize enhancement
  const initKlaviyoEnhancement = () => {
    // Find existing widgets
    const widgets = document.querySelectorAll(CONFIG.selectors.widget);
    widgets.forEach(enhanceKlaviyoWidget);

    // Watch for dynamically added widgets
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node is a widget
            if (node.matches && node.matches(CONFIG.selectors.widget)) {
              enhanceKlaviyoWidget(node);
            }
            
            // Check for widgets within the added node
            const widgets = node.querySelectorAll && node.querySelectorAll(CONFIG.selectors.widget);
            if (widgets) {
              widgets.forEach(enhanceKlaviyoWidget);
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  };

  // Start when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initKlaviyoEnhancement);
  } else {
    initKlaviyoEnhancement();
  }

  // Also try after a short delay to catch late-loading widgets
  setTimeout(initKlaviyoEnhancement, 1000);

})();
